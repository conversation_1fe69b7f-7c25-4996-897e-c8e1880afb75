/* App Component Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 0; /* <PERSON><PERSON> has fixed positioning with body padding */
}

/* Global layout utilities */
.section {
  padding: var(--space-16) 0;
}

.section--sm {
  padding: var(--space-12) 0;
}

.section--lg {
  padding: var(--space-24) 0;
}

.section--hero {
  padding: var(--space-20) 0 var(--space-16);
}

/* Responsive container */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

/* Page transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}
