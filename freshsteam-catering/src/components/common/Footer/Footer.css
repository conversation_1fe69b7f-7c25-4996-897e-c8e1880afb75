/* Footer Component Styles */
.footer {
  background-color: var(--color-gray-900);
  color: var(--text-white);
  margin-top: auto;
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  padding: var(--space-16) 0 var(--space-12);
}

.footer__section {
  text-align: center;
}

.footer__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer__subtitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-white);
  margin-bottom: var(--space-4);
}

.footer__description {
  color: var(--color-gray-400);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.footer__links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__links li {
  margin-bottom: var(--space-3);
}

.footer__link {
  color: var(--color-gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: var(--font-size-base);
}

.footer__link:hover {
  color: var(--color-primary);
}

/* Contact Info */
.footer__contact {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  align-items: center;
}

.footer__contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: var(--color-gray-400);
  font-size: var(--font-size-base);
}

.footer__contact-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
  flex-shrink: 0;
}

/* Footer Bottom */
.footer__bottom {
  border-top: 1px solid var(--color-gray-800);
  padding: var(--space-6) 0;
}

.footer__bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
}

.footer__copyright {
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
  margin: 0;
}

.footer__legal {
  display: flex;
  gap: var(--space-6);
}

.footer__legal-link {
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.footer__legal-link:hover {
  color: var(--color-primary);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .footer__content {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-12);
    text-align: left;
  }

  .footer__section {
    text-align: left;
  }

  .footer__description {
    margin-left: 0;
    margin-right: 0;
  }

  .footer__contact {
    align-items: flex-start;
  }

  .footer__bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .footer__content {
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: var(--space-16);
  }

  .footer__description {
    max-width: none;
  }
}

/* Social Media (placeholder for future implementation) */
.footer__social {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

@media (min-width: 768px) {
  .footer__social {
    justify-content: flex-start;
  }
}
