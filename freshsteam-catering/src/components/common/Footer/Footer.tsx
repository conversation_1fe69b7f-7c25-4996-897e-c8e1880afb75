import React from 'react';
import { Link } from 'react-router-dom';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';
import './Footer.css';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer__content">
          {/* Company Info */}
          <div className="footer__section">
            <h3 className="footer__title">FreshSteam Catering</h3>
            <p className="footer__description">
              Exceptional catering for your special moments. We bring fresh, 
              delicious cuisine and professional service to make your events memorable.
            </p>
            <div className="footer__social">
              {/* Social media links would go here */}
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer__section">
            <h4 className="footer__subtitle">Quick Links</h4>
            <ul className="footer__links">
              <li><Link to="/" className="footer__link">Home</Link></li>
              <li><Link to="/menu" className="footer__link">Menu & Ordering</Link></li>
              <li><Link to="/about" className="footer__link">About Us</Link></li>
              <li><Link to="/contact" className="footer__link">Contact</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div className="footer__section">
            <h4 className="footer__subtitle">Our Services</h4>
            <ul className="footer__links">
              <li><span className="footer__link">Corporate Events</span></li>
              <li><span className="footer__link">Wedding Catering</span></li>
              <li><span className="footer__link">Private Parties</span></li>
              <li><span className="footer__link">Special Events</span></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer__section">
            <h4 className="footer__subtitle">Contact Info</h4>
            <div className="footer__contact">
              <div className="footer__contact-item">
                <PhoneIcon className="footer__contact-icon" />
                <span>(*************</span>
              </div>
              <div className="footer__contact-item">
                <EnvelopeIcon className="footer__contact-icon" />
                <span><EMAIL></span>
              </div>
              <div className="footer__contact-item">
                <MapPinIcon className="footer__contact-icon" />
                <span>San Francisco Bay Area</span>
              </div>
              <div className="footer__contact-item">
                <ClockIcon className="footer__contact-icon" />
                <span>Mon-Sat: 8AM-8PM</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <p className="footer__copyright">
              © {currentYear} FreshSteam Catering. All rights reserved.
            </p>
            <div className="footer__legal">
              <span className="footer__legal-link">Privacy Policy</span>
              <span className="footer__legal-link">Terms of Service</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
