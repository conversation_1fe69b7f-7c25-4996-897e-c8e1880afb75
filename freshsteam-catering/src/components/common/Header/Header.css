/* Header Component Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background-color: var(--bg-primary);
  transition: all var(--transition-normal);
  border-bottom: 1px solid transparent;
}

.header--scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom-color: var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
  transition: padding var(--transition-normal);
}

.header--scrolled .header__content {
  padding: var(--space-3) 0;
}

/* Logo */
.header__logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-2xl);
  font-family: var(--font-primary);
  transition: color var(--transition-fast);
}

.header__logo:hover {
  color: var(--color-primary);
}

.header__logo-text {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Navigation */
.header__nav--desktop {
  display: none;
}

.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-8);
}

.header__nav-item {
  margin: 0;
}

.header__nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  padding: var(--space-2) 0;
  position: relative;
  transition: color var(--transition-fast);
}

.header__nav-link:hover {
  color: var(--color-primary);
}

.header__nav-link--active {
  color: var(--color-primary);
}

.header__nav-link--active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--radius-sm);
}

/* CTA Button */
.header__cta {
  display: none;
}

/* Mobile Menu Toggle */
.header__menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

.header__menu-toggle:hover {
  color: var(--color-primary);
}

.header__menu-icon {
  width: 24px;
  height: 24px;
}

/* Mobile Navigation */
.header__nav--mobile {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-lg);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.header__nav--open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.header__nav--mobile .header__nav-list {
  flex-direction: column;
  gap: 0;
  padding: var(--space-4) 0;
}

.header__nav--mobile .header__nav-item {
  border-bottom: 1px solid var(--color-gray-200);
}

.header__nav--mobile .header__nav-item:last-child {
  border-bottom: none;
}

.header__nav--mobile .header__nav-link {
  display: block;
  padding: var(--space-4) var(--space-4);
  font-size: var(--font-size-lg);
}

.header__nav-item--cta {
  padding: var(--space-4) var(--space-4) 0;
  border-bottom: none !important;
}

.header__nav-item--cta .btn {
  width: 100%;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .header__nav--desktop {
    display: block;
  }

  .header__cta {
    display: block;
  }

  .header__menu-toggle {
    display: none;
  }

  .header__nav--mobile {
    display: none;
  }

  .header__nav-list {
    gap: var(--space-6);
  }
}

@media (min-width: 1024px) {
  .header__nav-list {
    gap: var(--space-8);
  }
}

/* Smooth scroll offset for fixed header */
html {
  scroll-padding-top: 80px;
}

/* Body padding to account for fixed header */
body {
  padding-top: 80px;
}
