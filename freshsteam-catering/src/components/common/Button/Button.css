/* Button Component Styles */
.btn {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-medium);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 90, 95, 0.2);
}

/* But<PERSON> Variants */
.btn--primary {
  background-color: var(--color-primary);
  color: var(--text-white);
  border: 2px solid var(--color-primary);
}

.btn--primary:hover:not(.btn--disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn--secondary {
  background-color: var(--color-secondary);
  color: var(--text-white);
  border: 2px solid var(--color-secondary);
}

.btn--secondary:hover:not(.btn--disabled) {
  background-color: var(--color-secondary-dark);
  border-color: var(--color-secondary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn--outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn--outline:hover:not(.btn--disabled) {
  background-color: var(--color-primary);
  color: var(--text-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn--ghost {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid transparent;
}

.btn--ghost:hover:not(.btn--disabled) {
  background-color: var(--color-gray-100);
  color: var(--color-primary-dark);
}

/* Button Sizes */
.btn--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn--md {
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.btn--lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Button States */
.btn--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Button with icon support */
.btn .btn-icon {
  margin-right: var(--space-2);
}

.btn .btn-icon:last-child {
  margin-right: 0;
  margin-left: var(--space-2);
}

.btn .btn-icon:only-child {
  margin: 0;
}

/* Loading state */
.btn--loading {
  color: transparent;
}

.btn--loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .btn--lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    min-height: 48px;
  }
  
  .btn--md {
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-sm);
    min-height: 42px;
  }
}
