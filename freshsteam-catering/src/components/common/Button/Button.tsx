import React from 'react';
import { Link } from 'react-router-dom';
import { ButtonProps } from '../../../types';
import './Button.css';

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
  disabled = false,
  type = 'button',
  className = '',
  href,
  to,
  as,
  ...props
}) => {
  const baseClass = 'btn';
  const variantClass = `btn--${variant}`;
  const sizeClass = `btn--${size}`;
  const disabledClass = disabled ? 'btn--disabled' : '';

  const buttonClass = [
    baseClass,
    variantClass,
    sizeClass,
    disabledClass,
    className
  ].filter(Boolean).join(' ');

  // Render as external link
  if (href) {
    return (
      <a
        href={href}
        className={buttonClass}
        onClick={onClick}
        {...props}
      >
        {children}
      </a>
    );
  }

  // Render as React Router Link
  if (to) {
    return (
      <Link
        to={to}
        className={buttonClass}
        onClick={onClick}
        {...props}
      >
        {children}
      </Link>
    );
  }

  // Render as button (default)
  return (
    <button
      type={type}
      className={buttonClass}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
