import React, { useState } from 'react';
import { FunnelIcon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { MenuFilter as MenuFilterType, MenuCategory, DietaryInfo, Allergen } from '../../../types';
import { getCategoryDisplayName } from '../../../utils/menuUtils';
import Button from '../../common/Button';
import './MenuFilter.css';

interface MenuFilterProps {
  filter: MenuFilterType;
  onFilterChange: (filter: MenuFilterType) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  itemCount: number;
  totalCount: number;
}

const MenuFilter: React.FC<MenuFilterProps> = ({
  filter,
  onFilterChange,
  searchTerm,
  onSearchChange,
  itemCount,
  totalCount
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const categories: MenuCategory[] = ['appetizers', 'mains', 'sides', 'desserts', 'beverages'];
  const dietaryOptions: DietaryInfo[] = ['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'keto', 'low-carb', 'organic'];
  const allergenOptions: Allergen[] = ['nuts', 'dairy', 'gluten', 'eggs', 'soy', 'shellfish', 'fish'];

  const handleCategoryChange = (category: MenuCategory) => {
    onFilterChange({
      ...filter,
      category: filter.category === category ? undefined : category
    });
  };

  const handleDietaryChange = (dietary: DietaryInfo) => {
    const currentDietary = filter.dietaryInfo || [];
    const newDietary = currentDietary.includes(dietary)
      ? currentDietary.filter(d => d !== dietary)
      : [...currentDietary, dietary];
    
    onFilterChange({
      ...filter,
      dietaryInfo: newDietary.length > 0 ? newDietary : undefined
    });
  };

  const handleAllergenFreeChange = (allergen: Allergen) => {
    const currentAllergenFree = filter.allergenFree || [];
    const newAllergenFree = currentAllergenFree.includes(allergen)
      ? currentAllergenFree.filter(a => a !== allergen)
      : [...currentAllergenFree, allergen];
    
    onFilterChange({
      ...filter,
      allergenFree: newAllergenFree.length > 0 ? newAllergenFree : undefined
    });
  };

  const handleAvailabilityChange = (available: boolean) => {
    onFilterChange({
      ...filter,
      available: filter.available === available ? undefined : available
    });
  };

  const handleFeaturedChange = (featured: boolean) => {
    onFilterChange({
      ...filter,
      featured: filter.featured === featured ? undefined : featured
    });
  };

  const handlePriceRangeChange = (min: number, max: number) => {
    onFilterChange({
      ...filter,
      priceRange: { min, max }
    });
  };

  const clearAllFilters = () => {
    onFilterChange({});
    onSearchChange('');
  };

  const hasActiveFilters = () => {
    return !!(
      filter.category ||
      filter.dietaryInfo?.length ||
      filter.allergenFree?.length ||
      filter.available !== undefined ||
      filter.featured !== undefined ||
      filter.priceRange ||
      searchTerm
    );
  };

  return (
    <div className="menu-filter">
      <div className="menu-filter__header">
        <div className="menu-filter__search">
          <div className="menu-filter__search-input">
            <MagnifyingGlassIcon className="menu-filter__search-icon" />
            <input
              type="text"
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="menu-filter__search-field"
            />
            {searchTerm && (
              <button
                onClick={() => onSearchChange('')}
                className="menu-filter__search-clear"
                aria-label="Clear search"
              >
                <XMarkIcon className="menu-filter__search-clear-icon" />
              </button>
            )}
          </div>
        </div>

        <div className="menu-filter__controls">
          <div className="menu-filter__results">
            <span className="menu-filter__count">
              {itemCount} of {totalCount} items
            </span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="menu-filter__toggle"
          >
            <FunnelIcon className="menu-filter__toggle-icon" />
            Filters
            {hasActiveFilters() && (
              <span className="menu-filter__active-indicator"></span>
            )}
          </Button>

          {hasActiveFilters() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="menu-filter__clear"
            >
              Clear All
            </Button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="menu-filter__panel">
          {/* Categories */}
          <div className="menu-filter__section">
            <h3 className="menu-filter__section-title">Categories</h3>
            <div className="menu-filter__options">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className={`menu-filter__option ${
                    filter.category === category ? 'menu-filter__option--active' : ''
                  }`}
                >
                  {getCategoryDisplayName(category)}
                </button>
              ))}
            </div>
          </div>

          {/* Dietary Options */}
          <div className="menu-filter__section">
            <h3 className="menu-filter__section-title">Dietary Preferences</h3>
            <div className="menu-filter__options">
              {dietaryOptions.map((dietary) => (
                <button
                  key={dietary}
                  onClick={() => handleDietaryChange(dietary)}
                  className={`menu-filter__option ${
                    filter.dietaryInfo?.includes(dietary) ? 'menu-filter__option--active' : ''
                  }`}
                >
                  {dietary.charAt(0).toUpperCase() + dietary.slice(1).replace('-', ' ')}
                </button>
              ))}
            </div>
          </div>

          {/* Allergen-Free Options */}
          <div className="menu-filter__section">
            <h3 className="menu-filter__section-title">Allergen-Free</h3>
            <div className="menu-filter__options">
              {allergenOptions.map((allergen) => (
                <button
                  key={allergen}
                  onClick={() => handleAllergenFreeChange(allergen)}
                  className={`menu-filter__option ${
                    filter.allergenFree?.includes(allergen) ? 'menu-filter__option--active' : ''
                  }`}
                >
                  {allergen.charAt(0).toUpperCase() + allergen.slice(1)} Free
                </button>
              ))}
            </div>
          </div>

          {/* Availability & Featured */}
          <div className="menu-filter__section">
            <h3 className="menu-filter__section-title">Availability</h3>
            <div className="menu-filter__options">
              <button
                onClick={() => handleAvailabilityChange(true)}
                className={`menu-filter__option ${
                  filter.available === true ? 'menu-filter__option--active' : ''
                }`}
              >
                Available Only
              </button>
              <button
                onClick={() => handleFeaturedChange(true)}
                className={`menu-filter__option ${
                  filter.featured === true ? 'menu-filter__option--active' : ''
                }`}
              >
                Featured Items
              </button>
            </div>
          </div>

          {/* Price Range */}
          <div className="menu-filter__section">
            <h3 className="menu-filter__section-title">Price Range</h3>
            <div className="menu-filter__price-options">
              <button
                onClick={() => handlePriceRangeChange(0, 15)}
                className={`menu-filter__option ${
                  filter.priceRange?.min === 0 && filter.priceRange?.max === 15 ? 'menu-filter__option--active' : ''
                }`}
              >
                Under $15
              </button>
              <button
                onClick={() => handlePriceRangeChange(15, 30)}
                className={`menu-filter__option ${
                  filter.priceRange?.min === 15 && filter.priceRange?.max === 30 ? 'menu-filter__option--active' : ''
                }`}
              >
                $15 - $30
              </button>
              <button
                onClick={() => handlePriceRangeChange(30, 50)}
                className={`menu-filter__option ${
                  filter.priceRange?.min === 30 && filter.priceRange?.max === 50 ? 'menu-filter__option--active' : ''
                }`}
              >
                $30 - $50
              </button>
              <button
                onClick={() => handlePriceRangeChange(50, 1000)}
                className={`menu-filter__option ${
                  filter.priceRange?.min === 50 && filter.priceRange?.max === 1000 ? 'menu-filter__option--active' : ''
                }`}
              >
                Over $50
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuFilter;
