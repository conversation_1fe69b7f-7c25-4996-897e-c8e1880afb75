/* Menu Filter Component Styles */
.menu-filter {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  margin-bottom: var(--space-8);
}

.menu-filter__header {
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.menu-filter__search {
  flex: 1;
}

.menu-filter__search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.menu-filter__search-icon {
  position: absolute;
  left: var(--space-3);
  width: 20px;
  height: 20px;
  color: var(--text-muted);
  z-index: 2;
}

.menu-filter__search-field {
  width: 100%;
  padding: var(--space-3) var(--space-12) var(--space-3) var(--space-10);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.menu-filter__search-field:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(255, 90, 95, 0.1);
}

.menu-filter__search-clear {
  position: absolute;
  right: var(--space-3);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  color: var(--text-muted);
  transition: color var(--transition-fast);
}

.menu-filter__search-clear:hover {
  color: var(--text-secondary);
}

.menu-filter__search-clear-icon {
  width: 16px;
  height: 16px;
}

.menu-filter__controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.menu-filter__results {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.menu-filter__count {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

.menu-filter__toggle {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.menu-filter__toggle-icon {
  width: 16px;
  height: 16px;
}

.menu-filter__active-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--color-primary);
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
}

.menu-filter__clear {
  color: var(--color-primary);
}

/* Filter Panel */
.menu-filter__panel {
  border-top: 1px solid var(--color-gray-200);
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.menu-filter__section {
  margin-bottom: var(--space-6);
}

.menu-filter__section:last-child {
  margin-bottom: 0;
}

.menu-filter__section-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.menu-filter__options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.menu-filter__price-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-2);
}

.menu-filter__option {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.menu-filter__option:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.menu-filter__option--active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--text-white);
}

.menu-filter__option--active:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  color: var(--text-white);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .menu-filter__header {
    flex-direction: row;
    align-items: center;
  }

  .menu-filter__search {
    max-width: 400px;
  }

  .menu-filter__controls {
    flex-wrap: nowrap;
  }

  .menu-filter__price-options {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .menu-filter__panel {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
  }

  .menu-filter__section {
    margin-bottom: var(--space-4);
  }
}

@media (min-width: 1200px) {
  .menu-filter__panel {
    grid-template-columns: repeat(3, 1fr);
  }
}
