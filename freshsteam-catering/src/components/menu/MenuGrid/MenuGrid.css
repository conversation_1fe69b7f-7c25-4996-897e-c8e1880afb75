/* Menu Grid Component Styles */
.menu-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.menu-grid__empty {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--space-16);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--color-gray-300);
}

.menu-grid__empty-content h3 {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.menu-grid__empty-content p {
  color: var(--text-muted);
  margin: 0;
}

/* Menu Item Card */
.menu-item-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  position: relative;
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.menu-item-card--unavailable {
  opacity: 0.7;
}

.menu-item-card--unavailable:hover {
  transform: none;
  box-shadow: var(--shadow-md);
}

.menu-item-card__image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.menu-item-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-slow);
}

.menu-item-card:hover .menu-item-card__image img {
  transform: scale(1.05);
}

.menu-item-card--unavailable .menu-item-card__image img {
  filter: grayscale(100%);
}

.menu-item-card__overlay {
  position: absolute;
  bottom: var(--space-3);
  right: var(--space-3);
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.menu-item-card__price {
  background: rgba(255, 255, 255, 0.95);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  backdrop-filter: blur(10px);
}

.menu-item-card__featured {
  background: var(--color-secondary);
  color: var(--text-white);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item-card__badges {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.menu-item-card__badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  background-color: var(--color-secondary);
}

.menu-item-card__badge--vegetarian {
  background-color: #4CAF50;
}

.menu-item-card__badge--vegan {
  background-color: #8BC34A;
}

.menu-item-card__badge--gluten-free {
  background-color: #FF9800;
}

.menu-item-card__badge--dairy-free {
  background-color: #2196F3;
}

.menu-item-card__badge--keto {
  background-color: #9C27B0;
}

.menu-item-card__badge--low-carb {
  background-color: #795548;
}

.menu-item-card__badge--organic {
  background-color: #4CAF50;
}

.menu-item-card__unavailable {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.menu-item-card__content {
  padding: var(--space-5);
}

.menu-item-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  line-height: var(--line-height-tight);
}

.menu-item-card__description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
}

.menu-item-card__details {
  margin-bottom: var(--space-5);
}

.menu-item-card__detail {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-xs);
}

.menu-item-card__detail:last-child {
  margin-bottom: 0;
}

.menu-item-card__detail--allergens {
  color: var(--color-primary);
}

.menu-item-card__detail-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  min-width: 50px;
  flex-shrink: 0;
}

.menu-item-card__detail-value {
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.menu-item-card__actions {
  display: flex;
  gap: var(--space-2);
}

.menu-item-card__actions .btn {
  flex: 1;
  font-size: var(--font-size-xs);
}

.menu-item-card__add-btn {
  flex: 2;
}

.menu-item-card__details-btn {
  flex: 1;
}

.menu-item-card__unavailable-btn {
  width: 100%;
}

/* Responsive Grid */
@media (min-width: 640px) {
  .menu-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
  }
}

@media (min-width: 1024px) {
  .menu-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-10);
  }
}

@media (min-width: 1400px) {
  .menu-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Hover effects */
.menu-item-card__actions .btn:hover {
  transform: translateY(-1px);
}

/* Loading state */
.menu-item-card--loading {
  background: var(--color-gray-100);
  animation: pulse 1.5s ease-in-out infinite;
}

.menu-item-card--loading .menu-item-card__image {
  background: var(--color-gray-200);
}

.menu-item-card--loading .menu-item-card__content {
  background: var(--color-gray-100);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .menu-item-card {
    transition: none;
  }
  
  .menu-item-card:hover {
    transform: none;
  }
  
  .menu-item-card__image img {
    transition: none;
  }
  
  .menu-item-card:hover .menu-item-card__image img {
    transform: none;
  }
  
  .menu-item-card__actions .btn:hover {
    transform: none;
  }
  
  .menu-item-card--loading {
    animation: none;
  }
}
