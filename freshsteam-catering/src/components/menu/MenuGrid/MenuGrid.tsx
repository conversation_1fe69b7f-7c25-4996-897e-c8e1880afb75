import React from 'react';
import { MenuItem } from '../../../types';
import { formatPrice, getDietaryInfoDisplay, getAllergenDisplay } from '../../../utils/menuUtils';
import Button from '../../common/Button';
import './MenuGrid.css';

interface MenuGridProps {
  items: MenuItem[];
  onAddToOrder?: (item: MenuItem) => void;
  onViewDetails?: (item: MenuItem) => void;
}

const MenuGrid: React.FC<MenuGridProps> = ({ 
  items, 
  onAddToOrder, 
  onViewDetails 
}) => {
  if (items.length === 0) {
    return (
      <div className="menu-grid__empty">
        <div className="menu-grid__empty-content">
          <h3>No items found</h3>
          <p>Try adjusting your filters to see more menu items.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="menu-grid">
      {items.map((item) => (
        <div key={item.id} className={`menu-item-card ${!item.available ? 'menu-item-card--unavailable' : ''}`}>
          <div className="menu-item-card__image">
            <img 
              src={item.image} 
              alt={item.name}
              onError={(e) => {
                // Fallback to placeholder if image fails to load
                const target = e.target as HTMLImageElement;
                target.src = `https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop&crop=center`;
              }}
            />
            <div className="menu-item-card__overlay">
              <span className="menu-item-card__price">{formatPrice(item.price)}</span>
              {item.featured && (
                <span className="menu-item-card__featured">Featured</span>
              )}
            </div>
            
            {/* Dietary badges */}
            {item.dietaryInfo.length > 0 && (
              <div className="menu-item-card__badges">
                {item.dietaryInfo.slice(0, 3).map((diet) => (
                  <span key={diet} className={`menu-item-card__badge menu-item-card__badge--${diet}`}>
                    {diet === 'vegetarian' ? 'V' : 
                     diet === 'vegan' ? 'VG' : 
                     diet === 'gluten-free' ? 'GF' : 
                     diet === 'dairy-free' ? 'DF' :
                     diet === 'keto' ? 'K' :
                     diet.charAt(0).toUpperCase()}
                  </span>
                ))}
              </div>
            )}
            
            {!item.available && (
              <div className="menu-item-card__unavailable">
                <span>Currently Unavailable</span>
              </div>
            )}
          </div>
          
          <div className="menu-item-card__content">
            <h3 className="menu-item-card__title">{item.name}</h3>
            <p className="menu-item-card__description">{item.description}</p>
            
            <div className="menu-item-card__details">
              {item.servingSize && (
                <div className="menu-item-card__detail">
                  <span className="menu-item-card__detail-label">Serving:</span>
                  <span className="menu-item-card__detail-value">{item.servingSize}</span>
                </div>
              )}
              
              {item.preparationTime && (
                <div className="menu-item-card__detail">
                  <span className="menu-item-card__detail-label">Prep Time:</span>
                  <span className="menu-item-card__detail-value">{item.preparationTime} min</span>
                </div>
              )}
              
              {item.dietaryInfo.length > 0 && (
                <div className="menu-item-card__detail">
                  <span className="menu-item-card__detail-label">Dietary:</span>
                  <span className="menu-item-card__detail-value">
                    {getDietaryInfoDisplay(item.dietaryInfo)}
                  </span>
                </div>
              )}
              
              {item.allergens.length > 0 && (
                <div className="menu-item-card__detail menu-item-card__detail--allergens">
                  <span className="menu-item-card__detail-label">Allergens:</span>
                  <span className="menu-item-card__detail-value">
                    {getAllergenDisplay(item.allergens)}
                  </span>
                </div>
              )}
            </div>
            
            <div className="menu-item-card__actions">
              {item.available ? (
                <>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => onAddToOrder?.(item)}
                    className="menu-item-card__add-btn"
                  >
                    Add to Order
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewDetails?.(item)}
                    className="menu-item-card__details-btn"
                  >
                    Details
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  disabled
                  className="menu-item-card__unavailable-btn"
                >
                  Unavailable
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MenuGrid;
