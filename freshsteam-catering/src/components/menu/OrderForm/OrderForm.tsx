import React, { useState } from 'react';
import { ShoppingCartIcon, CalendarIcon, UserIcon } from '@heroicons/react/24/outline';
import { MenuItem, OrderCalculation } from '../../../types';
import { calculateOrder, formatPrice } from '../../../utils/menuUtils';
import Button from '../../common/Button';
import './OrderForm.css';

interface OrderFormProps {
  selectedItems?: { menuItem: MenuItem; quantity: number }[];
  onOrderSubmit?: (orderData: any) => void;
}

const OrderForm: React.FC<OrderFormProps> = ({ 
  selectedItems = [], 
  onOrderSubmit 
}) => {
  const [orderCalculation, setOrderCalculation] = useState<OrderCalculation | null>(
    selectedItems.length > 0 ? calculateOrder(selectedItems) : null
  );

  // Google Form URL - This would be replaced with actual Google Form
  const GOOGLE_FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSdummy-form-id/viewform?embedded=true";

  const handleOrderCalculation = () => {
    if (selectedItems.length > 0) {
      const calculation = calculateOrder(selectedItems);
      setOrderCalculation(calculation);
    }
  };

  return (
    <section id="order-form" className="order-form section">
      <div className="container">
        <div className="order-form__header">
          <h2 className="order-form__title">
            <ShoppingCartIcon className="order-form__title-icon" />
            Place Your Order
          </h2>
          <p className="order-form__subtitle">
            Ready to bring exceptional catering to your event? Fill out our order form below 
            and we'll get back to you within 24 hours with a detailed quote.
          </p>
        </div>

        <div className="order-form__content">
          {/* Order Summary (if items selected) */}
          {orderCalculation && (
            <div className="order-form__summary">
              <h3 className="order-form__summary-title">Order Summary</h3>
              <div className="order-form__summary-items">
                {orderCalculation.items.map(({ menuItem, quantity }) => (
                  <div key={menuItem.id} className="order-form__summary-item">
                    <span className="order-form__summary-item-name">
                      {menuItem.name} × {quantity}
                    </span>
                    <span className="order-form__summary-item-price">
                      {formatPrice(menuItem.price * quantity)}
                    </span>
                  </div>
                ))}
              </div>
              <div className="order-form__summary-totals">
                <div className="order-form__summary-line">
                  <span>Subtotal:</span>
                  <span>{formatPrice(orderCalculation.subtotal)}</span>
                </div>
                <div className="order-form__summary-line">
                  <span>Tax (8%):</span>
                  <span>{formatPrice(orderCalculation.tax)}</span>
                </div>
                <div className="order-form__summary-line order-form__summary-line--total">
                  <span>Total:</span>
                  <span>{formatPrice(orderCalculation.total)}</span>
                </div>
                <div className="order-form__summary-prep">
                  <span>Estimated Prep Time: {orderCalculation.estimatedPrepTime} minutes</span>
                </div>
              </div>
            </div>
          )}

          {/* Google Form Integration */}
          <div className="order-form__form-container">
            <div className="order-form__form-header">
              <h3>Event & Contact Information</h3>
              <p>
                Please provide your event details and contact information. 
                We'll use this to create a customized quote for your catering needs.
              </p>
            </div>

            {/* Placeholder for Google Form - In production, this would be an actual embedded form */}
            <div className="order-form__google-form">
              <div className="order-form__form-placeholder">
                <div className="order-form__form-demo">
                  <h4>📋 Catering Order Form</h4>
                  <p className="order-form__form-note">
                    <strong>Note:</strong> This is a demo interface. In the live version, 
                    this would be a fully integrated Google Form for seamless order processing.
                  </p>
                  
                  <div className="order-form__demo-sections">
                    <div className="order-form__demo-section">
                      <h5>
                        <UserIcon className="order-form__demo-icon" />
                        Customer Information
                      </h5>
                      <ul>
                        <li>Name, Email, Phone</li>
                        <li>Company/Organization (if applicable)</li>
                        <li>Preferred contact method</li>
                      </ul>
                    </div>

                    <div className="order-form__demo-section">
                      <h5>
                        <CalendarIcon className="order-form__demo-icon" />
                        Event Details
                      </h5>
                      <ul>
                        <li>Event date and time</li>
                        <li>Event location and address</li>
                        <li>Number of guests</li>
                        <li>Event type (Corporate, Wedding, etc.)</li>
                      </ul>
                    </div>

                    <div className="order-form__demo-section">
                      <h5>
                        <ShoppingCartIcon className="order-form__demo-icon" />
                        Menu Selection
                      </h5>
                      <ul>
                        <li>Appetizers, mains, sides, desserts</li>
                        <li>Quantity for each item</li>
                        <li>Special dietary requirements</li>
                        <li>Additional requests</li>
                      </ul>
                    </div>
                  </div>

                  <div className="order-form__demo-actions">
                    <Button variant="primary" size="lg" disabled>
                      Submit Order Request (Demo)
                    </Button>
                    <p className="order-form__demo-disclaimer">
                      In the live version, this form would connect directly to Google Forms 
                      for automatic order processing and email notifications.
                    </p>
                  </div>
                </div>
              </div>

              {/* This is where the actual Google Form would be embedded */}
              {/* 
              <iframe 
                src={GOOGLE_FORM_URL}
                width="100%" 
                height="800" 
                frameBorder="0" 
                marginHeight={0} 
                marginWidth={0}
                title="Catering Order Form"
                className="order-form__iframe"
              >
                Loading…
              </iframe>
              */}
            </div>
          </div>

          {/* Contact Information */}
          <div className="order-form__contact">
            <h3>Need Help with Your Order?</h3>
            <div className="order-form__contact-options">
              <div className="order-form__contact-option">
                <h4>📞 Call Us</h4>
                <p>(555) 123-4567</p>
                <span>Mon-Sat: 8AM-8PM</span>
              </div>
              <div className="order-form__contact-option">
                <h4>✉️ Email Us</h4>
                <p><EMAIL></p>
                <span>Response within 24 hours</span>
              </div>
              <div className="order-form__contact-option">
                <h4>💬 Live Chat</h4>
                <p>Available on our website</p>
                <span>Mon-Fri: 9AM-6PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OrderForm;
