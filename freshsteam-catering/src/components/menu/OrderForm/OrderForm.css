/* Order Form Component Styles */
.order-form {
  background: var(--bg-secondary);
}

.order-form__header {
  text-align: center;
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.order-form__title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.order-form__title-icon {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.order-form__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.order-form__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  max-width: 1000px;
  margin: 0 auto;
}

/* Order Summary */
.order-form__summary {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.order-form__summary-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--color-gray-200);
}

.order-form__summary-items {
  margin-bottom: var(--space-4);
}

.order-form__summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--color-gray-100);
}

.order-form__summary-item:last-child {
  border-bottom: none;
}

.order-form__summary-item-name {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.order-form__summary-item-price {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.order-form__summary-totals {
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--space-4);
}

.order-form__summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-base);
}

.order-form__summary-line--total {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  padding-top: var(--space-3);
  border-top: 1px solid var(--color-gray-200);
  margin-top: var(--space-3);
}

.order-form__summary-prep {
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: var(--color-gray-100);
  border-radius: var(--radius-md);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Form Container */
.order-form__form-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.order-form__form-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--bg-secondary);
}

.order-form__form-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.order-form__form-header p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Google Form Placeholder */
.order-form__google-form {
  min-height: 600px;
}

.order-form__form-placeholder {
  padding: var(--space-8);
}

.order-form__form-demo {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.order-form__form-demo h4 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.order-form__form-note {
  background: var(--color-gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-8);
  color: var(--text-secondary);
  border-left: 4px solid var(--color-primary);
}

.order-form__demo-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  text-align: left;
}

.order-form__demo-section {
  background: var(--bg-secondary);
  padding: var(--space-5);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

.order-form__demo-section h5 {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.order-form__demo-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
}

.order-form__demo-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-form__demo-section li {
  padding: var(--space-2) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--color-gray-200);
}

.order-form__demo-section li:last-child {
  border-bottom: none;
}

.order-form__demo-section li::before {
  content: '✓';
  color: var(--color-secondary);
  font-weight: var(--font-weight-bold);
  margin-right: var(--space-2);
}

.order-form__demo-actions {
  text-align: center;
}

.order-form__demo-disclaimer {
  margin-top: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-style: italic;
}

/* Actual iframe styles (for when Google Form is embedded) */
.order-form__iframe {
  border: none;
  width: 100%;
  min-height: 800px;
  background: var(--bg-primary);
}

/* Contact Options */
.order-form__contact {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  text-align: center;
}

.order-form__contact h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.order-form__contact-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.order-form__contact-option {
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

.order-form__contact-option h4 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.order-form__contact-option p {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--space-1);
}

.order-form__contact-option span {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .order-form__content {
    grid-template-columns: 1fr 2fr;
    gap: var(--space-12);
    align-items: start;
  }

  .order-form__demo-sections {
    grid-template-columns: 1fr;
  }

  .order-form__contact-options {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .order-form__title {
    font-size: var(--font-size-5xl);
  }

  .order-form__title-icon {
    width: 40px;
    height: 40px;
  }
}
