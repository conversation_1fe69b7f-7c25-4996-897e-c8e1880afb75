/* Services Component Styles */
.services {
  background-color: var(--bg-secondary);
}

.section__header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.services__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-16);
}

.service-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.service-card__image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.service-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-slow);
}

.service-card:hover .service-card__image img {
  transform: scale(1.05);
}

.service-card__overlay {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: rgba(255, 255, 255, 0.95);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.service-card__price {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.service-card__content {
  padding: var(--space-6);
}

.service-card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.service-card__description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-5);
}

.service-card__features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-6) 0;
}

.service-card__feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.service-card__feature:last-child {
  margin-bottom: 0;
}

.service-card__feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: var(--color-secondary);
  color: var(--text-white);
  border-radius: 50%;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.service-card__actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.service-card__actions .btn {
  flex: 1;
  min-width: 120px;
}

/* Services CTA */
.services__cta {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-xl);
  padding: var(--space-12);
  text-align: center;
  color: var(--text-white);
}

.services__cta-content h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
  color: var(--text-white);
}

.services__cta-content p {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
  color: rgba(255, 255, 255, 0.9);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.services__cta .btn {
  background-color: var(--text-white);
  color: var(--color-primary);
  border-color: var(--text-white);
}

.services__cta .btn:hover {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-100);
  transform: translateY(-2px);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .services__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-10);
  }

  .section__title {
    font-size: var(--font-size-5xl);
  }

  .service-card__actions {
    flex-wrap: nowrap;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .services__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-12);
  }

  .service-card__image {
    height: 240px;
  }
}

@media (min-width: 1200px) {
  .services__grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .service-card__image {
    height: 200px;
  }

  .service-card__actions {
    flex-direction: column;
  }

  .service-card__actions .btn {
    flex: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .service-card {
    transition: none;
  }
  
  .service-card:hover {
    transform: none;
  }
  
  .service-card__image img {
    transition: none;
  }
  
  .service-card:hover .service-card__image img {
    transform: none;
  }
}
