import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';
import { testimonials } from '../../../data/menuData';
import { Testimonial } from '../../../types';
import './Testimonials.css';

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-advance testimonials
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToPrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
  };

  const goToSlide = (index: number) => {
    setIsAutoPlaying(false);
    setCurrentIndex(index);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`testimonial__star ${
          index < rating ? 'testimonial__star--filled' : 'testimonial__star--empty'
        }`}
      />
    ));
  };

  return (
    <section className="testimonials section">
      <div className="container">
        <div className="section__header">
          <h2 className="section__title">What Our Clients Say</h2>
          <p className="section__subtitle">
            Trusted by hundreds of satisfied customers who have experienced 
            our exceptional catering services
          </p>
        </div>
        
        <div className="testimonials__carousel">
          <div className="testimonials__container">
            <div 
              className="testimonials__track"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial: Testimonial) => (
                <div key={testimonial.id} className="testimonial-card">
                  <div className="testimonial-card__content">
                    <div className="testimonial-card__rating">
                      {renderStars(testimonial.rating)}
                    </div>
                    
                    <blockquote className="testimonial-card__quote">
                      "{testimonial.comment}"
                    </blockquote>
                    
                    <div className="testimonial-card__author">
                      {testimonial.image && (
                        <div className="testimonial-card__avatar">
                          <img 
                            src={testimonial.image} 
                            alt={testimonial.name}
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement;
                              target.src = `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face`;
                            }}
                          />
                        </div>
                      )}
                      <div className="testimonial-card__author-info">
                        <cite className="testimonial-card__name">{testimonial.name}</cite>
                        <span className="testimonial-card__event">{testimonial.event}</span>
                        <span className="testimonial-card__date">
                          {new Date(testimonial.date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long'
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Navigation Controls */}
          <div className="testimonials__controls">
            <button 
              className="testimonials__nav testimonials__nav--prev"
              onClick={goToPrevious}
              aria-label="Previous testimonial"
            >
              <ChevronLeftIcon className="testimonials__nav-icon" />
            </button>
            
            <button 
              className="testimonials__nav testimonials__nav--next"
              onClick={goToNext}
              aria-label="Next testimonial"
            >
              <ChevronRightIcon className="testimonials__nav-icon" />
            </button>
          </div>
          
          {/* Dots Indicator */}
          <div className="testimonials__dots">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`testimonials__dot ${
                  index === currentIndex ? 'testimonials__dot--active' : ''
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
        
        <div className="testimonials__stats">
          <div className="testimonials__stat">
            <span className="testimonials__stat-number">500+</span>
            <span className="testimonials__stat-label">Happy Clients</span>
          </div>
          <div className="testimonials__stat">
            <span className="testimonials__stat-number">98%</span>
            <span className="testimonials__stat-label">Satisfaction Rate</span>
          </div>
          <div className="testimonials__stat">
            <span className="testimonials__stat-number">4.9</span>
            <span className="testimonials__stat-label">Average Rating</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
