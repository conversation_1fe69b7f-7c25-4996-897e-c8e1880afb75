/* Hero Component Styles */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 90, 95, 0.8) 0%,
    rgba(0, 166, 153, 0.6) 100%
  );
  z-index: 2;
}

.hero .container {
  position: relative;
  z-index: 3;
  width: 100%;
}

.hero__content {
  text-align: center;
  color: var(--text-white);
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-8) 0;
}

.hero__text {
  margin-bottom: var(--space-12);
}

.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-6);
  color: var(--text-white);
}

.hero__title-highlight {
  background: linear-gradient(135deg, #FFE066, #FF6B6B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.hero__subtitle {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-relaxed);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-normal);
}

.hero__stats {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
  flex-wrap: wrap;
}

.hero__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
}

.hero__stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  font-family: var(--font-primary);
}

.hero__stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-weight-medium);
}

.hero__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.hero__cta-primary,
.hero__cta-secondary {
  min-width: 200px;
}

.hero__features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-6);
  max-width: 400px;
  margin: 0 auto;
}

.hero__feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform var(--transition-fast);
}

.hero__feature:hover {
  transform: translateY(-2px);
}

.hero__feature-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-1);
}

.hero__feature span {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-white);
  text-align: center;
}

.hero__scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: bounce 2s infinite;
}

.hero__scroll-arrow {
  width: 24px;
  height: 24px;
  border-right: 2px solid var(--text-white);
  border-bottom: 2px solid var(--text-white);
  transform: rotate(45deg);
  opacity: 0.7;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Tablet Styles */
@media (min-width: 768px) {
  .hero__title {
    font-size: var(--font-size-5xl);
  }

  .hero__subtitle {
    font-size: var(--font-size-xl);
  }

  .hero__features {
    grid-template-columns: repeat(4, 1fr);
    max-width: 600px;
  }

  .hero__actions {
    flex-wrap: nowrap;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .hero__title {
    font-size: var(--font-size-6xl);
  }

  .hero__content {
    max-width: 900px;
  }

  .hero__stats {
    gap: var(--space-12);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .hero__scroll-indicator {
    animation: none;
  }
  
  .hero__feature {
    transition: none;
  }
  
  .hero__feature:hover {
    transform: none;
  }
}
