import React from 'react';
import { Link } from 'react-router-dom';
import { getFeaturedMenuItems, formatPrice, getDietaryInfoDisplay } from '../../../utils/menuUtils';
import { menuItems } from '../../../data/menuData';
import Button from '../../common/Button';
import './FeaturedMenu.css';

const FeaturedMenu: React.FC = () => {
  const featuredItems = getFeaturedMenuItems(menuItems);

  return (
    <section className="featured-menu section">
      <div className="container">
        <div className="section__header">
          <h2 className="section__title">Featured Menu Items</h2>
          <p className="section__subtitle">
            A taste of our most popular dishes, crafted with the finest ingredients 
            and prepared by our expert chefs
          </p>
        </div>
        
        <div className="featured-menu__grid">
          {featuredItems.map((item) => (
            <div key={item.id} className="menu-card">
              <div className="menu-card__image">
                <img 
                  src={item.image} 
                  alt={item.name}
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.src = `https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop&crop=center`;
                  }}
                />
                <div className="menu-card__overlay">
                  <span className="menu-card__price">{formatPrice(item.price)}</span>
                </div>
                {item.dietaryInfo.length > 0 && (
                  <div className="menu-card__badges">
                    {item.dietaryInfo.slice(0, 2).map((diet) => (
                      <span key={diet} className={`menu-card__badge menu-card__badge--${diet}`}>
                        {diet === 'vegetarian' ? 'V' : 
                         diet === 'vegan' ? 'VG' : 
                         diet === 'gluten-free' ? 'GF' : 
                         diet.charAt(0).toUpperCase()}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="menu-card__content">
                <h3 className="menu-card__title">{item.name}</h3>
                <p className="menu-card__description">{item.description}</p>
                
                {item.servingSize && (
                  <div className="menu-card__serving">
                    <span className="menu-card__serving-label">Serving:</span>
                    <span className="menu-card__serving-value">{item.servingSize}</span>
                  </div>
                )}
                
                {item.dietaryInfo.length > 0 && (
                  <div className="menu-card__dietary">
                    <span className="menu-card__dietary-label">Dietary:</span>
                    <span className="menu-card__dietary-value">
                      {getDietaryInfoDisplay(item.dietaryInfo)}
                    </span>
                  </div>
                )}
                
                <div className="menu-card__actions">
                  <button className="btn btn--primary btn--sm">
                    Add to Order
                  </button>
                  <button className="btn btn--ghost btn--sm">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="featured-menu__cta">
          <h3>Explore Our Complete Menu</h3>
          <p>
            Discover our full range of appetizers, main courses, desserts, and beverages. 
            Each dish is carefully crafted to deliver exceptional flavor and presentation.
          </p>
          <Link to="/menu">
            <Button variant="primary" size="lg">
              View Full Menu
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedMenu;
