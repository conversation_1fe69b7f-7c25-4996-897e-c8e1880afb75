import React from 'react';
import { services } from '../../../data/menuData';
import { Service } from '../../../types';
import Button from '../../common/Button';
import './Services.css';

const Services: React.FC = () => {
  return (
    <section className="services section">
      <div className="container">
        <div className="section__header">
          <h2 className="section__title">Our Catering Services</h2>
          <p className="section__subtitle">
            Professional catering solutions for every occasion, 
            tailored to make your event extraordinary
          </p>
        </div>
        
        <div className="services__grid">
          {services.map((service: Service) => (
            <div key={service.id} className="service-card">
              <div className="service-card__image">
                <img 
                  src={service.image} 
                  alt={service.name}
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.src = `https://images.unsplash.com/photo-1555244162-803834f70033?w=400&h=300&fit=crop&crop=center`;
                  }}
                />
                <div className="service-card__overlay">
                  <span className="service-card__price">{service.priceRange}</span>
                </div>
              </div>
              
              <div className="service-card__content">
                <h3 className="service-card__title">{service.name}</h3>
                <p className="service-card__description">{service.description}</p>
                
                <ul className="service-card__features">
                  {service.features.map((feature, index) => (
                    <li key={index} className="service-card__feature">
                      <span className="service-card__feature-icon">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <div className="service-card__actions">
                  <Button variant="primary" size="sm">
                    Learn More
                  </Button>
                  <Button variant="outline" size="sm">
                    Get Quote
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="services__cta">
          <div className="services__cta-content">
            <h3>Need a Custom Solution?</h3>
            <p>
              Every event is unique. Let us create a personalized catering 
              experience that perfectly matches your vision and requirements.
            </p>
            <Button variant="secondary" size="lg" to="/contact">
              Contact Us for Custom Catering
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
