import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../../common/Button';
import './Hero.css';

const Hero: React.FC = () => {
  const scrollToOrderForm = () => {
    const element = document.getElementById('order-form');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Navigate to menu page if order form not found on current page
      window.location.href = '/menu#order-form';
    }
  };

  return (
    <section className="hero">
      <div className="hero__background">
        <div className="hero__overlay"></div>
        <img 
          src="/images/hero/hero-background.jpg" 
          alt="Delicious catered food" 
          className="hero__image"
          onError={(e) => {
            // Fallback to a solid color background if image fails to load
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
          }}
        />
      </div>
      
      <div className="container">
        <div className="hero__content">
          <div className="hero__text">
            <h1 className="hero__title">
              Exceptional Catering for Your 
              <span className="hero__title-highlight"> Special Moments</span>
            </h1>
            <p className="hero__subtitle">
              Fresh, delicious cuisine and professional service that transforms 
              every event into an unforgettable experience. From intimate gatherings 
              to grand celebrations, we bring culinary excellence to your table.
            </p>
            <div className="hero__stats">
              <div className="hero__stat">
                <span className="hero__stat-number">500+</span>
                <span className="hero__stat-label">Events Catered</span>
              </div>
              <div className="hero__stat">
                <span className="hero__stat-number">98%</span>
                <span className="hero__stat-label">Client Satisfaction</span>
              </div>
              <div className="hero__stat">
                <span className="hero__stat-number">5★</span>
                <span className="hero__stat-label">Average Rating</span>
              </div>
            </div>
          </div>
          
          <div className="hero__actions">
            <Button
              variant="primary"
              size="lg"
              onClick={scrollToOrderForm}
              className="hero__cta-primary"
            >
              View Menu & Order
            </Button>
            <Link to="/about">
              <Button
                variant="outline"
                size="lg"
                className="hero__cta-secondary"
              >
                Learn More
              </Button>
            </Link>
          </div>
          
          <div className="hero__features">
            <div className="hero__feature">
              <div className="hero__feature-icon">🍽️</div>
              <span>Fresh Ingredients</span>
            </div>
            <div className="hero__feature">
              <div className="hero__feature-icon">👨‍🍳</div>
              <span>Expert Chefs</span>
            </div>
            <div className="hero__feature">
              <div className="hero__feature-icon">🚚</div>
              <span>Full Service</span>
            </div>
            <div className="hero__feature">
              <div className="hero__feature-icon">⭐</div>
              <span>5-Star Quality</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="hero__scroll-indicator">
        <div className="hero__scroll-arrow"></div>
      </div>
    </section>
  );
};

export default Hero;
