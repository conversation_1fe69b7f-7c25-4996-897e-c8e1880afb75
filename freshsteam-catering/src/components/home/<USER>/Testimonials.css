/* Testimonials Component Styles */
.testimonials {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  position: relative;
  overflow: hidden;
}

.testimonials::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f7f7f7' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
  z-index: 1;
}

.testimonials .container {
  position: relative;
  z-index: 2;
}

.testimonials__carousel {
  position: relative;
  margin-bottom: var(--space-16);
}

.testimonials__container {
  overflow: hidden;
  border-radius: var(--radius-xl);
  background: var(--bg-primary);
  box-shadow: var(--shadow-xl);
}

.testimonials__track {
  display: flex;
  transition: transform var(--transition-slow);
}

.testimonial-card {
  min-width: 100%;
  padding: var(--space-12);
  text-align: center;
}

.testimonial-card__content {
  max-width: 600px;
  margin: 0 auto;
}

.testimonial-card__rating {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
  margin-bottom: var(--space-6);
}

.testimonial__star {
  width: 20px;
  height: 20px;
}

.testimonial__star--filled {
  color: #FFD700;
}

.testimonial__star--empty {
  color: var(--color-gray-300);
}

.testimonial-card__quote {
  font-size: var(--font-size-xl);
  font-style: italic;
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
  margin: 0 0 var(--space-8) 0;
  position: relative;
  font-weight: var(--font-weight-medium);
}

.testimonial-card__quote::before {
  content: '"';
  font-size: var(--font-size-6xl);
  color: var(--color-primary);
  position: absolute;
  top: -20px;
  left: -30px;
  font-family: Georgia, serif;
  opacity: 0.3;
}

.testimonial-card__author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.testimonial-card__avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--color-primary);
  flex-shrink: 0;
}

.testimonial-card__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.testimonial-card__author-info {
  text-align: left;
}

.testimonial-card__name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-style: normal;
  margin-bottom: var(--space-1);
}

.testimonial-card__event {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.testimonial-card__date {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Navigation Controls */
.testimonials__controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 3;
}

.testimonials__nav {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--bg-primary);
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-fast);
  pointer-events: auto;
  box-shadow: var(--shadow-md);
}

.testimonials__nav:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
  transform: scale(1.1);
}

.testimonials__nav:hover .testimonials__nav-icon {
  color: var(--text-white);
}

.testimonials__nav--prev {
  margin-left: -24px;
}

.testimonials__nav--next {
  margin-right: -24px;
}

.testimonials__nav-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

/* Dots Indicator */
.testimonials__dots {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
  margin-top: var(--space-6);
}

.testimonials__dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: var(--color-gray-300);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.testimonials__dot--active {
  background: var(--color-primary);
  transform: scale(1.2);
}

.testimonials__dot:hover {
  background: var(--color-primary);
}

/* Stats */
.testimonials__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
  text-align: center;
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

.testimonials__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.testimonials__stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-family: var(--font-primary);
}

.testimonials__stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* Mobile Styles */
@media (max-width: 767px) {
  .testimonial-card {
    padding: var(--space-8);
  }

  .testimonial-card__quote {
    font-size: var(--font-size-lg);
  }

  .testimonial-card__quote::before {
    font-size: var(--font-size-4xl);
    top: -15px;
    left: -20px;
  }

  .testimonial-card__author {
    flex-direction: column;
    text-align: center;
  }

  .testimonial-card__author-info {
    text-align: center;
  }

  .testimonials__nav {
    display: none;
  }

  .testimonials__stats {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
}

/* Tablet Styles */
@media (min-width: 768px) {
  .testimonials__stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .testimonials__track {
    transition: none;
  }
  
  .testimonials__nav {
    transition: none;
  }
  
  .testimonials__nav:hover {
    transform: none;
  }
  
  .testimonials__dot {
    transition: none;
  }
  
  .testimonials__dot--active {
    transform: none;
  }
}
