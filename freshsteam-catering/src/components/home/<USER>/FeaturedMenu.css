/* Featured Menu Component Styles */
.featured-menu {
  background-color: var(--bg-primary);
}

.featured-menu__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-16);
}

.menu-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  position: relative;
}

.menu-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.menu-card__image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.menu-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-slow);
}

.menu-card:hover .menu-card__image img {
  transform: scale(1.05);
}

.menu-card__overlay {
  position: absolute;
  bottom: var(--space-4);
  right: var(--space-4);
  background: rgba(255, 255, 255, 0.95);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.menu-card__price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.menu-card__badges {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  display: flex;
  gap: var(--space-2);
}

.menu-card__badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  background-color: var(--color-secondary);
}

.menu-card__badge--vegetarian {
  background-color: #4CAF50;
}

.menu-card__badge--vegan {
  background-color: #8BC34A;
}

.menu-card__badge--gluten-free {
  background-color: #FF9800;
}

.menu-card__badge--dairy-free {
  background-color: #2196F3;
}

.menu-card__badge--keto {
  background-color: #9C27B0;
}

.menu-card__content {
  padding: var(--space-6);
}

.menu-card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  line-height: var(--line-height-tight);
}

.menu-card__description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-base);
}

.menu-card__serving,
.menu-card__dietary {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
}

.menu-card__serving-label,
.menu-card__dietary-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  min-width: 60px;
}

.menu-card__serving-value,
.menu-card__dietary-value {
  color: var(--text-secondary);
}

.menu-card__actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-5);
}

.menu-card__actions .btn {
  flex: 1;
}

/* Featured Menu CTA */
.featured-menu__cta {
  text-align: center;
  background: var(--bg-secondary);
  padding: var(--space-12);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--color-gray-300);
}

.featured-menu__cta h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.featured-menu__cta p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .featured-menu__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-10);
  }

  .menu-card__actions {
    flex-direction: row;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .featured-menu__grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-12);
  }

  .menu-card__image {
    height: 200px;
  }
}

@media (min-width: 1200px) {
  .featured-menu__grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .menu-card__actions {
    flex-direction: column;
  }
}

/* Hover effects for interactive elements */
.menu-card__actions .btn:hover {
  transform: translateY(-1px);
}

/* Loading state placeholder */
.menu-card--loading {
  background: var(--color-gray-100);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .menu-card {
    transition: none;
  }
  
  .menu-card:hover {
    transform: none;
  }
  
  .menu-card__image img {
    transition: none;
  }
  
  .menu-card:hover .menu-card__image img {
    transform: none;
  }
  
  .menu-card__actions .btn:hover {
    transform: none;
  }
  
  .menu-card--loading {
    animation: none;
  }
}
