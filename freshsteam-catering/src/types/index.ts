// Menu and ordering types
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: MenuCategory;
  image: string;
  available: boolean;
  allergens: Allergen[];
  dietaryInfo: DietaryInfo[];
  preparationTime?: number; // in minutes
  servingSize?: string;
  featured?: boolean;
}

export type MenuCategory = 
  | 'appetizers' 
  | 'mains' 
  | 'desserts' 
  | 'beverages' 
  | 'sides';

export type Allergen = 
  | 'nuts' 
  | 'dairy' 
  | 'gluten' 
  | 'eggs' 
  | 'soy' 
  | 'shellfish' 
  | 'fish';

export type DietaryInfo = 
  | 'vegetarian' 
  | 'vegan' 
  | 'gluten-free' 
  | 'dairy-free' 
  | 'keto' 
  | 'low-carb' 
  | 'organic';

export interface MenuFilter {
  category?: MenuCategory;
  dietaryInfo?: DietaryInfo[];
  allergenFree?: Allergen[];
  priceRange?: {
    min: number;
    max: number;
  };
  available?: boolean;
  featured?: boolean;
}

// Service and business types
export interface Service {
  id: string;
  name: string;
  description: string;
  image: string;
  features: string[];
  priceRange: string;
}

export interface Testimonial {
  id: string;
  name: string;
  event: string;
  rating: number;
  comment: string;
  date: string;
  image?: string;
}

// Contact and order types
export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  message: string;
}

export interface OrderCalculation {
  items: { menuItem: MenuItem; quantity: number }[];
  subtotal: number;
  tax: number;
  total: number;
  estimatedPrepTime: number;
}

// Navigation and UI types
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  href?: string;
  to?: string;
  as?: 'button' | 'link';
}
