/* Contact Page Styles */
.contact-page {
  min-height: 100vh;
}

/* Contact Methods Section */
.contact-methods {
  background: var(--bg-secondary);
}

.contact-methods__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.contact-method-card {
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: transform var(--transition-normal);
}

.contact-method-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.contact-method-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--color-primary);
  border-radius: 50%;
  margin: 0 auto var(--space-4);
}

.contact-method-card__icon-svg {
  width: 28px;
  height: 28px;
  color: var(--text-white);
}

.contact-method-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.contact-method-card__primary {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--space-1);
}

.contact-method-card__secondary {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--space-4);
}

.contact-method-card__action {
  width: 100%;
}

/* Contact Content */
.contact-content {
  background: var(--bg-primary);
}

.contact-content__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
}

/* Contact Form */
.contact-form {
  background: var(--bg-secondary);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.contact-form__header {
  margin-bottom: var(--space-8);
  text-align: center;
}

.contact-form__header h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.contact-form__header p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.contact-form__form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background: var(--bg-primary);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(255, 90, 95, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-form__submit {
  width: 100%;
  margin-top: var(--space-4);
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.contact-info__section {
  background: var(--bg-secondary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.contact-info__section h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.contact-info__hours {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.contact-info__hour {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-gray-200);
}

.contact-info__hour:last-child {
  border-bottom: none;
}

.contact-info__hour span:first-child {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.contact-info__hour span:last-child {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.contact-info__coverage p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-3);
}

.contact-info__coverage ul {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-4) 0;
}

.contact-info__coverage li {
  padding: var(--space-1) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--space-5);
}

.contact-info__coverage li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-secondary);
  font-weight: var(--font-weight-bold);
}

.contact-info__coverage-note {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-style: italic;
  margin: 0;
}

/* FAQ Section */
.faq {
  background: var(--bg-secondary);
}

.section__title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.section__title-icon {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.faq__list {
  max-width: 800px;
  margin: 0 auto var(--space-12);
}

.faq__item {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.faq__question {
  width: 100%;
  padding: var(--space-5) var(--space-6);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  transition: background-color var(--transition-fast);
}

.faq__question:hover {
  background-color: var(--bg-secondary);
}

.faq__chevron {
  width: 20px;
  height: 20px;
  color: var(--text-muted);
  transition: transform var(--transition-fast);
  flex-shrink: 0;
}

.faq__chevron--expanded {
  transform: rotate(180deg);
}

.faq__answer {
  padding: 0 var(--space-6) var(--space-5);
  border-top: 1px solid var(--color-gray-200);
  background: var(--bg-secondary);
}

.faq__answer p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: var(--space-4) 0 0;
}

.faq__cta {
  text-align: center;
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.faq__cta p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .contact-methods__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
  }

  .contact-content__grid {
    grid-template-columns: 2fr 1fr;
    gap: var(--space-16);
  }

  .form-row {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }

  .contact-form__submit {
    width: auto;
    align-self: flex-start;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .contact-methods__grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-10);
  }

  .section__title {
    font-size: var(--font-size-4xl);
  }

  .section__title-icon {
    width: 40px;
    height: 40px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .contact-method-card {
    transition: none;
  }
  
  .contact-method-card:hover {
    transform: none;
  }
  
  .faq__chevron {
    transition: none;
  }
  
  .faq__chevron--expanded {
    transform: none;
  }
}
