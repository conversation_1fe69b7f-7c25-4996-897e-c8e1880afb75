/* Home Page Styles */
.home {
  min-height: 100vh;
}

/* CTA Section */
.cta {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: var(--text-white);
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E") repeat;
  z-index: 1;
}

.cta .container {
  position: relative;
  z-index: 2;
}

.cta__content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.cta__content h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  margin-bottom: var(--space-4);
}

.cta__content p {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-relaxed);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
}

.cta__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta__actions .btn {
  min-width: 200px;
}

.cta__actions .btn--primary {
  background-color: var(--text-white);
  color: var(--color-primary);
  border-color: var(--text-white);
}

.cta__actions .btn--primary:hover {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-100);
  transform: translateY(-2px);
}

.cta__actions .btn--outline {
  background-color: transparent;
  color: var(--text-white);
  border-color: var(--text-white);
}

.cta__actions .btn--outline:hover {
  background-color: var(--text-white);
  color: var(--color-primary);
  transform: translateY(-2px);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .cta__content h2 {
    font-size: var(--font-size-5xl);
  }

  .cta__actions {
    flex-wrap: nowrap;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .cta__content {
    max-width: 700px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .cta__actions .btn:hover {
    transform: none;
  }
}
