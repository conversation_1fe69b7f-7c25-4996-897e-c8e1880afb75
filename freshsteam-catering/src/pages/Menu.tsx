import React, { useState, useMemo } from 'react';
import { MenuItem, MenuFilter as MenuFilterType } from '../types';
import { menuItems } from '../data/menuData';
import { filterMenuItems, searchMenuItems, getCategoryDisplayName } from '../utils/menuUtils';
import MenuFilter from '../components/menu/MenuFilter';
import MenuGrid from '../components/menu/MenuGrid';
import OrderForm from '../components/menu/OrderForm';
import './Menu.css';

const Menu: React.FC = () => {
  const [filter, setFilter] = useState<MenuFilterType>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<{ menuItem: MenuItem; quantity: number }[]>([]);

  // Filter and search menu items
  const filteredItems = useMemo(() => {
    let items = filterMenuItems(menuItems, filter);
    if (searchTerm) {
      items = searchMenuItems(items, searchTerm);
    }
    return items;
  }, [filter, searchTerm]);

  // Group items by category for display
  const itemsByCategory = useMemo(() => {
    const categories = ['appetizers', 'mains', 'sides', 'desserts', 'beverages'] as const;
    return categories.reduce((acc, category) => {
      const categoryItems = filteredItems.filter(item => item.category === category);
      if (categoryItems.length > 0) {
        acc[category] = categoryItems;
      }
      return acc;
    }, {} as Record<string, MenuItem[]>);
  }, [filteredItems]);

  const handleAddToOrder = (item: MenuItem) => {
    setSelectedItems(prev => {
      const existingItem = prev.find(selected => selected.menuItem.id === item.id);
      if (existingItem) {
        return prev.map(selected =>
          selected.menuItem.id === item.id
            ? { ...selected, quantity: selected.quantity + 1 }
            : selected
        );
      } else {
        return [...prev, { menuItem: item, quantity: 1 }];
      }
    });
  };

  const handleViewDetails = (item: MenuItem) => {
    // This could open a modal or navigate to a detail page
    console.log('View details for:', item.name);
  };

  return (
    <div className="menu-page">
      <section className="page-header section--sm">
        <div className="container">
          <h1 className="page-header__title">Our Menu & Ordering</h1>
          <p className="page-header__subtitle">
            Discover our delicious offerings crafted with the finest ingredients.
            Filter by category, dietary preferences, or search for specific items.
          </p>
        </div>
      </section>

      <section className="menu-content section">
        <div className="container">
          <MenuFilter
            filter={filter}
            onFilterChange={setFilter}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            itemCount={filteredItems.length}
            totalCount={menuItems.length}
          />

          {Object.keys(itemsByCategory).length > 0 ? (
            <div className="menu-categories">
              {Object.entries(itemsByCategory).map(([category, items]) => (
                <div key={category} className="menu-category">
                  <h2 className="menu-category__title">
                    {getCategoryDisplayName(category as any)}
                  </h2>
                  <MenuGrid
                    items={items}
                    onAddToOrder={handleAddToOrder}
                    onViewDetails={handleViewDetails}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="menu-empty">
              <h3>No menu items found</h3>
              <p>Try adjusting your filters or search terms to see more items.</p>
            </div>
          )}
        </div>
      </section>

      <OrderForm
        selectedItems={selectedItems}
        onOrderSubmit={(orderData) => {
          console.log('Order submitted:', orderData);
        }}
      />
    </div>
  );
};

export default Menu;
