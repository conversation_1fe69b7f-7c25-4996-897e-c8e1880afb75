import React from 'react';

const Home: React.FC = () => {
  return (
    <div className="home">
      <section className="hero section--hero">
        <div className="container">
          <div className="hero__content">
            <h1 className="hero__title">
              Exceptional Catering for Your Special Moments
            </h1>
            <p className="hero__subtitle">
              Fresh, delicious cuisine and professional service that makes every event memorable
            </p>
            <div className="hero__actions">
              <button className="btn btn--primary btn--lg">
                View Menu & Order
              </button>
              <button className="btn btn--outline btn--lg">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      <section className="services section">
        <div className="container">
          <div className="section__header">
            <h2>Our Catering Services</h2>
            <p>Professional catering for every occasion</p>
          </div>
          <div className="services__grid">
            {/* Service cards will be added here */}
            <div className="service-card">
              <h3>Corporate Events</h3>
              <p>Professional catering for business meetings and conferences</p>
            </div>
            <div className="service-card">
              <h3>Wedding Catering</h3>
              <p>Elegant dining experiences for your special day</p>
            </div>
            <div className="service-card">
              <h3>Private Parties</h3>
              <p>Intimate gatherings with personalized service</p>
            </div>
            <div className="service-card">
              <h3>Special Events</h3>
              <p>Milestone celebrations and unique occasions</p>
            </div>
          </div>
        </div>
      </section>

      <section className="featured-menu section">
        <div className="container">
          <div className="section__header">
            <h2>Featured Menu Items</h2>
            <p>A taste of our most popular dishes</p>
          </div>
          <div className="menu__grid">
            {/* Featured menu items will be added here */}
            <div className="menu-card">
              <h4>Herb-Crusted Salmon</h4>
              <p>Atlantic salmon with fresh herb crust and lemon butter sauce</p>
              <span className="price">$28.99</span>
            </div>
            <div className="menu-card">
              <h4>Braised Short Ribs</h4>
              <p>Slow-braised beef with red wine reduction</p>
              <span className="price">$34.99</span>
            </div>
            <div className="menu-card">
              <h4>Chocolate Lava Cake</h4>
              <p>Warm chocolate cake with molten center</p>
              <span className="price">$8.99</span>
            </div>
          </div>
        </div>
      </section>

      <section className="testimonials section">
        <div className="container">
          <div className="section__header">
            <h2>What Our Clients Say</h2>
            <p>Trusted by hundreds of satisfied customers</p>
          </div>
          <div className="testimonials__grid">
            {/* Testimonials will be added here */}
            <div className="testimonial-card">
              <p>"FreshSteam exceeded our expectations! The food was incredible."</p>
              <cite>- Sarah Johnson, Corporate Event</cite>
            </div>
            <div className="testimonial-card">
              <p>"Our wedding day was perfect thanks to FreshSteam."</p>
              <cite>- Michael & Emma Chen, Wedding</cite>
            </div>
          </div>
        </div>
      </section>

      <section className="cta section">
        <div className="container">
          <div className="cta__content">
            <h2>Ready to Plan Your Event?</h2>
            <p>Get started with our easy online ordering system</p>
            <button className="btn btn--primary btn--lg">
              Order Now
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
