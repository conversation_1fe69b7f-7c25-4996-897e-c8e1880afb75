import React from 'react';
import { Link } from 'react-router-dom';
import Hero from '../components/home/<USER>';
import Services from '../components/home/<USER>';
import FeaturedMenu from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import Button from '../components/common/Button';
import './Home.css';

const Home: React.FC = () => {
  return (
    <div className="home">
      <Hero />
      <Services />
      <FeaturedMenu />
      <Testimonials />

      <section className="cta section">
        <div className="container">
          <div className="cta__content">
            <h2>Ready to Plan Your Event?</h2>
            <p>
              Get started with our easy online ordering system and let us create
              an unforgettable culinary experience for your special occasion.
            </p>
            <div className="cta__actions">
              <Link to="/menu">
                <Button variant="primary" size="lg">
                  Order Now
                </Button>
              </Link>
              <Link to="/contact">
                <Button variant="outline" size="lg">
                  Get Custom Quote
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
