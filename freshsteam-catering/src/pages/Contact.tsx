import React from 'react';

const Contact: React.FC = () => {
  return (
    <div className="contact-page">
      <section className="page-header section">
        <div className="container">
          <h1>Contact Us</h1>
          <p>Get in touch to discuss your catering needs</p>
        </div>
      </section>

      <section className="contact-content section">
        <div className="container">
          <div className="contact-info">
            <h2>Get In Touch</h2>
            <div className="contact-details">
              <div className="contact-item">
                <h3>Phone</h3>
                <p>(*************</p>
              </div>
              <div className="contact-item">
                <h3>Email</h3>
                <p><EMAIL></p>
              </div>
              <div className="contact-item">
                <h3>Service Area</h3>
                <p>San Francisco Bay Area</p>
              </div>
              <div className="contact-item">
                <h3>Hours</h3>
                <p>Monday - Saturday: 8AM - 8PM</p>
              </div>
            </div>
          </div>

          <div className="contact-form">
            <h2>Send Us a Message</h2>
            <form className="form">
              <div className="form-group">
                <label htmlFor="name">Name</label>
                <input type="text" id="name" name="name" required />
              </div>
              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input type="email" id="email" name="email" required />
              </div>
              <div className="form-group">
                <label htmlFor="phone">Phone</label>
                <input type="tel" id="phone" name="phone" />
              </div>
              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea id="message" name="message" rows={5} required></textarea>
              </div>
              <button type="submit" className="btn btn--primary">
                Send Message
              </button>
            </form>
          </div>

          <div className="faq">
            <h2>Frequently Asked Questions</h2>
            <div className="faq-item">
              <h3>How far in advance should I book?</h3>
              <p>We recommend booking at least 2-3 weeks in advance for best availability.</p>
            </div>
            <div className="faq-item">
              <h3>Do you accommodate dietary restrictions?</h3>
              <p>Yes, we offer vegetarian, vegan, gluten-free, and other dietary options.</p>
            </div>
            <div className="faq-item">
              <h3>What is your service area?</h3>
              <p>We serve the San Francisco Bay Area and surrounding regions.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
