import React, { useState } from 'react';
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import Button from '../components/common/Button';
import './Contact.css';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    eventType: '',
    eventDate: '',
    guestCount: '',
    message: ''
  });

  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
    // In a real app, this would send the data to a server
    alert('Thank you for your message! We\'ll get back to you within 24 hours.');
  };

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  const contactMethods = [
    {
      icon: PhoneIcon,
      title: "Phone",
      primary: "(*************",
      secondary: "Mon-Sat: 8AM-8PM",
      action: "Call Now"
    },
    {
      icon: EnvelopeIcon,
      title: "Email",
      primary: "<EMAIL>",
      secondary: "Response within 24 hours",
      action: "Send Email"
    },
    {
      icon: ChatBubbleLeftRightIcon,
      title: "Live Chat",
      primary: "Available on website",
      secondary: "Mon-Fri: 9AM-6PM",
      action: "Start Chat"
    },
    {
      icon: MapPinIcon,
      title: "Service Area",
      primary: "San Francisco Bay Area",
      secondary: "50+ mile radius",
      action: "View Coverage"
    }
  ];

  const faqItems = [
    {
      question: "How far in advance should I book catering?",
      answer: "We recommend booking at least 2-3 weeks in advance for best availability, especially during peak seasons (spring and fall). For large events (100+ guests) or holidays, 4-6 weeks advance notice is preferred."
    },
    {
      question: "Do you accommodate dietary restrictions and allergies?",
      answer: "Absolutely! We specialize in accommodating various dietary needs including vegetarian, vegan, gluten-free, dairy-free, keto, and other restrictions. Please inform us of any allergies or dietary requirements when booking."
    },
    {
      question: "What is included in your catering service?",
      answer: "Our full-service catering includes menu planning, food preparation, delivery, setup, serving equipment, and cleanup. We can also provide additional services like servers, bartenders, and event coordination upon request."
    },
    {
      question: "What is your cancellation policy?",
      answer: "Cancellations made 72+ hours in advance receive a full refund. Cancellations within 48-72 hours are subject to a 50% charge. Cancellations within 48 hours are non-refundable due to food preparation and staff scheduling."
    },
    {
      question: "Do you provide equipment and serving staff?",
      answer: "Yes! We provide all necessary serving equipment, chafing dishes, utensils, and plates. Professional serving staff can be arranged for an additional fee. We'll discuss your specific needs during consultation."
    },
    {
      question: "How do you handle payment?",
      answer: "We require a 50% deposit to secure your date, with the balance due 48 hours before your event. We accept cash, check, and all major credit cards. Payment plans are available for larger events."
    },
    {
      question: "Can you accommodate last-minute orders?",
      answer: "While we prefer advance notice, we understand emergencies happen. Contact us directly for last-minute requests - we'll do our best to accommodate based on availability and menu complexity."
    },
    {
      question: "Do you offer tastings?",
      answer: "Yes! We offer complimentary tastings for events with 50+ guests. For smaller events, tastings are available for a small fee that's credited toward your final bill when you book with us."
    }
  ];

  return (
    <div className="contact-page">
      <section className="page-header section--sm">
        <div className="container">
          <h1 className="page-header__title">Contact Us</h1>
          <p className="page-header__subtitle">
            Ready to plan your perfect event? Get in touch and let's create something amazing together.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="contact-methods section">
        <div className="container">
          <div className="contact-methods__grid">
            {contactMethods.map((method, index) => (
              <div key={index} className="contact-method-card">
                <div className="contact-method-card__icon">
                  <method.icon className="contact-method-card__icon-svg" />
                </div>
                <div className="contact-method-card__content">
                  <h3 className="contact-method-card__title">{method.title}</h3>
                  <p className="contact-method-card__primary">{method.primary}</p>
                  <p className="contact-method-card__secondary">{method.secondary}</p>
                  <Button variant="outline" size="sm" className="contact-method-card__action">
                    {method.action}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="contact-content section">
        <div className="container">
          <div className="contact-content__grid">
            {/* Contact Form */}
            <div className="contact-form">
              <div className="contact-form__header">
                <h2>Send Us a Message</h2>
                <p>
                  Tell us about your event and we'll get back to you with a personalized quote
                  and recommendations within 24 hours.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="contact-form__form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="name">Full Name *</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      placeholder="Your full name"
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="email">Email Address *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="phone">Phone Number</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="(*************"
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="eventType">Event Type</label>
                    <select
                      id="eventType"
                      name="eventType"
                      value={formData.eventType}
                      onChange={handleInputChange}
                    >
                      <option value="">Select event type</option>
                      <option value="corporate">Corporate Event</option>
                      <option value="wedding">Wedding</option>
                      <option value="private">Private Party</option>
                      <option value="special">Special Occasion</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="eventDate">Event Date</label>
                    <input
                      type="date"
                      id="eventDate"
                      name="eventDate"
                      value={formData.eventDate}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="guestCount">Number of Guests</label>
                    <input
                      type="number"
                      id="guestCount"
                      name="guestCount"
                      value={formData.guestCount}
                      onChange={handleInputChange}
                      placeholder="e.g., 50"
                      min="1"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="message">Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    placeholder="Tell us about your event, dietary requirements, budget, or any special requests..."
                  />
                </div>

                <Button type="submit" variant="primary" size="lg" className="contact-form__submit">
                  Send Message
                </Button>
              </form>
            </div>

            {/* Business Info */}
            <div className="contact-info">
              <div className="contact-info__section">
                <h3>Business Hours</h3>
                <div className="contact-info__hours">
                  <div className="contact-info__hour">
                    <span>Monday - Friday</span>
                    <span>8:00 AM - 8:00 PM</span>
                  </div>
                  <div className="contact-info__hour">
                    <span>Saturday</span>
                    <span>9:00 AM - 6:00 PM</span>
                  </div>
                  <div className="contact-info__hour">
                    <span>Sunday</span>
                    <span>Closed</span>
                  </div>
                </div>
              </div>

              <div className="contact-info__section">
                <h3>Service Coverage</h3>
                <div className="contact-info__coverage">
                  <p>We proudly serve the entire San Francisco Bay Area, including:</p>
                  <ul>
                    <li>San Francisco</li>
                    <li>Oakland & East Bay</li>
                    <li>San Jose & South Bay</li>
                    <li>Peninsula & South Bay</li>
                    <li>Marin County</li>
                  </ul>
                  <p className="contact-info__coverage-note">
                    Special arrangements available for events outside our standard service area.
                  </p>
                </div>
              </div>

              <div className="contact-info__section">
                <h3>Emergency Contact</h3>
                <p>
                  For urgent matters on event day, call our emergency line at
                  <strong> (555) 123-HELP</strong>. Available 24/7 during active events.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq section">
        <div className="container">
          <div className="section__header">
            <h2 className="section__title">
              <QuestionMarkCircleIcon className="section__title-icon" />
              Frequently Asked Questions
            </h2>
            <p className="section__subtitle">
              Find answers to common questions about our catering services
            </p>
          </div>

          <div className="faq__list">
            {faqItems.map((item, index) => (
              <div key={index} className="faq__item">
                <button
                  className="faq__question"
                  onClick={() => toggleFaq(index)}
                  aria-expanded={expandedFaq === index}
                >
                  <span>{item.question}</span>
                  <ChevronDownIcon
                    className={`faq__chevron ${expandedFaq === index ? 'faq__chevron--expanded' : ''}`}
                  />
                </button>
                {expandedFaq === index && (
                  <div className="faq__answer">
                    <p>{item.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="faq__cta">
            <p>Still have questions? We're here to help!</p>
            <Button variant="primary" size="md">
              Contact Us Directly
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
