/* Menu Page Styles */
.menu-page {
  min-height: 100vh;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f7f7f7' fill-opacity='0.3'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E") repeat;
  z-index: 1;
}

.page-header .container {
  position: relative;
  z-index: 2;
}

.page-header__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.page-header__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin: 0 auto;
}

/* Menu Content */
.menu-content {
  background: var(--bg-primary);
}

.menu-categories {
  margin-top: var(--space-8);
}

.menu-category {
  margin-bottom: var(--space-16);
}

.menu-category:last-child {
  margin-bottom: 0;
}

.menu-category__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: center;
  position: relative;
}

.menu-category__title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-sm);
}

/* Empty State */
.menu-empty {
  text-align: center;
  padding: var(--space-16);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--color-gray-300);
  margin-top: var(--space-8);
}

.menu-empty h3 {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-3);
}

.menu-empty p {
  color: var(--text-muted);
  margin: 0;
}

/* Responsive Styles */
@media (min-width: 768px) {
  .page-header__title {
    font-size: var(--font-size-5xl);
  }

  .page-header__subtitle {
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px) {
  .page-header__title {
    font-size: var(--font-size-6xl);
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.menu-category__title:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 4px;
}

/* Print styles */
@media print {
  .menu-page {
    background: white;
  }
  
  .page-header {
    background: white;
    color: black;
  }
  
  .menu-filter {
    display: none;
  }
}
