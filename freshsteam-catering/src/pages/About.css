/* About Page Styles */
.about-page {
  min-height: 100vh;
}

/* Hero Story Section */
.about-hero {
  background: var(--bg-primary);
}

.about-hero__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: center;
}

.about-hero__text h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.about-hero__text p {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  margin-bottom: var(--space-5);
}

.about-hero__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
  margin-top: var(--space-8);
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.about-hero__stat {
  text-align: center;
}

.about-hero__stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-family: var(--font-primary);
}

.about-hero__stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

.about-hero__image {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.about-hero__image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  object-position: center;
}

/* Team Section */
.about-team {
  background: var(--bg-secondary);
}

.team-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

.team-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: transform var(--transition-normal);
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.team-card__image {
  height: 250px;
  overflow: hidden;
}

.team-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-slow);
}

.team-card:hover .team-card__image img {
  transform: scale(1.05);
}

.team-card__content {
  padding: var(--space-6);
  text-align: center;
}

.team-card__name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.team-card__role {
  font-size: var(--font-size-base);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-4);
}

.team-card__bio {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Process Section */
.about-process {
  background: var(--bg-primary);
}

.process-steps {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

.process-step {
  display: flex;
  gap: var(--space-6);
  align-items: flex-start;
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  position: relative;
}

.process-step::before {
  content: '';
  position: absolute;
  left: var(--space-6);
  top: calc(100% + var(--space-4));
  width: 2px;
  height: var(--space-4);
  background: var(--color-gray-300);
}

.process-step:last-child::before {
  display: none;
}

.process-step__icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--color-primary);
  border-radius: 50%;
  flex-shrink: 0;
}

.process-step__icon-svg {
  width: 24px;
  height: 24px;
  color: var(--text-white);
}

.process-step__number {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: var(--color-secondary);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  border: 2px solid var(--bg-primary);
}

.process-step__content {
  flex: 1;
}

.process-step__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.process-step__description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Certifications Section */
.about-certifications {
  background: var(--bg-secondary);
}

.certifications-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.certification-card {
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: transform var(--transition-normal);
}

.certification-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.certification-card__icon {
  width: 48px;
  height: 48px;
  color: var(--color-secondary);
  margin: 0 auto var(--space-4);
}

.certification-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.certification-card__description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* CTA Section */
.about-cta {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: var(--text-white);
}

.about-cta__content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.about-cta__content h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  margin-bottom: var(--space-4);
}

.about-cta__content p {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
}

.about-cta__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.about-cta__actions .btn {
  min-width: 180px;
}

.about-cta__actions .btn--primary {
  background-color: var(--text-white);
  color: var(--color-primary);
  border-color: var(--text-white);
}

.about-cta__actions .btn--primary:hover {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-100);
  transform: translateY(-2px);
}

.about-cta__actions .btn--outline {
  background-color: transparent;
  color: var(--text-white);
  border-color: var(--text-white);
}

.about-cta__actions .btn--outline:hover {
  background-color: var(--text-white);
  color: var(--color-primary);
  transform: translateY(-2px);
}

/* Tablet Styles */
@media (min-width: 768px) {
  .about-hero__content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
  }

  .about-hero__text h2 {
    font-size: var(--font-size-4xl);
  }

  .team-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-10);
  }

  .process-steps {
    gap: var(--space-12);
  }

  .certifications-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
  }

  .about-cta__actions {
    flex-wrap: nowrap;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .about-hero__text h2 {
    font-size: var(--font-size-5xl);
  }

  .about-hero__image img {
    height: 500px;
  }

  .about-cta__content h2 {
    font-size: var(--font-size-4xl);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .team-card {
    transition: none;
  }
  
  .team-card:hover {
    transform: none;
  }
  
  .team-card__image img {
    transition: none;
  }
  
  .team-card:hover .team-card__image img {
    transform: none;
  }
  
  .certification-card {
    transition: none;
  }
  
  .certification-card:hover {
    transform: none;
  }
  
  .about-cta__actions .btn:hover {
    transform: none;
  }
}
