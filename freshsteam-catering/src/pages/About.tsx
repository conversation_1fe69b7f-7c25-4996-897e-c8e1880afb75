import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  HeartIcon,
  StarIcon,
  CheckBadgeIcon,
  UsersIcon,
  ClockIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import Button from '../components/common/Button';
import './About.css';

const About: React.FC = () => {
  const teamMembers = [
    {
      name: "Chef <PERSON>",
      role: "Executive Chef & Founder",
      image: "https://images.unsplash.com/photo-1559339352-11d035aa65de?w=300&h=300&fit=crop&crop=face",
      bio: "With over 15 years of culinary experience, Chef <PERSON> brings passion and creativity to every dish."
    },
    {
      name: "<PERSON>",
      role: "Operations Manager",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
      bio: "<PERSON> ensures every event runs smoothly with his attention to detail and exceptional service standards."
    },
    {
      name: "<PERSON>",
      role: "Pastry Chef",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
      bio: "<PERSON> creates stunning desserts that are as beautiful as they are delicious."
    }
  ];

  const processSteps = [
    {
      step: 1,
      title: "Initial Consultation",
      description: "We discuss your event details, preferences, and dietary requirements to create the perfect menu.",
      icon: UsersIcon
    },
    {
      step: 2,
      title: "Menu Planning",
      description: "Our chefs design a customized menu that fits your budget, style, and guest count.",
      icon: HeartIcon
    },
    {
      step: 3,
      title: "Preparation",
      description: "We source the finest ingredients and prepare everything with meticulous attention to detail.",
      icon: ClockIcon
    },
    {
      step: 4,
      title: "Event Service",
      description: "Our professional team delivers and serves your meal, ensuring a seamless experience.",
      icon: StarIcon
    }
  ];

  const certifications = [
    {
      title: "Food Safety Certified",
      description: "All staff are certified in food safety and handling procedures",
      icon: ShieldCheckIcon
    },
    {
      title: "Licensed & Insured",
      description: "Fully licensed catering business with comprehensive insurance coverage",
      icon: CheckBadgeIcon
    },
    {
      title: "Health Department Approved",
      description: "Regular inspections and compliance with all health regulations",
      icon: CheckBadgeIcon
    }
  ];

  return (
    <div className="about-page">
      <section className="page-header section--sm">
        <div className="container">
          <h1 className="page-header__title">About FreshSteam</h1>
          <p className="page-header__subtitle">
            Our story, passion, and commitment to exceptional catering experiences
          </p>
        </div>
      </section>

      {/* Hero Story Section */}
      <section className="about-hero section">
        <div className="container">
          <div className="about-hero__content">
            <div className="about-hero__text">
              <h2>Our Story</h2>
              <p>
                FreshSteam Catering was born from a simple belief: every gathering deserves
                exceptional food that brings people together. Founded in 2018 by Chef Maria Rodriguez,
                our journey began with a passion for creating memorable culinary experiences that
                transform ordinary events into extraordinary celebrations.
              </p>
              <p>
                What started as a small home-based operation has grown into the Bay Area's
                trusted catering partner, serving over 500 events and delighting thousands
                of guests with our commitment to fresh ingredients, innovative flavors, and
                impeccable service.
              </p>
              <div className="about-hero__stats">
                <div className="about-hero__stat">
                  <span className="about-hero__stat-number">500+</span>
                  <span className="about-hero__stat-label">Events Catered</span>
                </div>
                <div className="about-hero__stat">
                  <span className="about-hero__stat-number">5,000+</span>
                  <span className="about-hero__stat-label">Happy Guests</span>
                </div>
                <div className="about-hero__stat">
                  <span className="about-hero__stat-number">6</span>
                  <span className="about-hero__stat-label">Years of Excellence</span>
                </div>
              </div>
            </div>
            <div className="about-hero__image">
              <img
                src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&crop=center"
                alt="Chef preparing food in kitchen"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="about-team section">
        <div className="container">
          <div className="section__header">
            <h2 className="section__title">Meet Our Team</h2>
            <p className="section__subtitle">
              The passionate professionals behind every exceptional catering experience
            </p>
          </div>

          <div className="team-grid">
            {teamMembers.map((member, index) => (
              <div key={index} className="team-card">
                <div className="team-card__image">
                  <img src={member.image} alt={member.name} />
                </div>
                <div className="team-card__content">
                  <h3 className="team-card__name">{member.name}</h3>
                  <p className="team-card__role">{member.role}</p>
                  <p className="team-card__bio">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="about-process section">
        <div className="container">
          <div className="section__header">
            <h2 className="section__title">Our Process</h2>
            <p className="section__subtitle">
              From consultation to celebration, we handle every detail with care
            </p>
          </div>

          <div className="process-steps">
            {processSteps.map((step, index) => (
              <div key={index} className="process-step">
                <div className="process-step__icon">
                  <step.icon className="process-step__icon-svg" />
                  <span className="process-step__number">{step.step}</span>
                </div>
                <div className="process-step__content">
                  <h3 className="process-step__title">{step.title}</h3>
                  <p className="process-step__description">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      <section className="about-certifications section">
        <div className="container">
          <div className="section__header">
            <h2 className="section__title">Quality & Certifications</h2>
            <p className="section__subtitle">
              We maintain the highest standards of food safety and professional service
            </p>
          </div>

          <div className="certifications-grid">
            {certifications.map((cert, index) => (
              <div key={index} className="certification-card">
                <cert.icon className="certification-card__icon" />
                <h3 className="certification-card__title">{cert.title}</h3>
                <p className="certification-card__description">{cert.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="about-cta section">
        <div className="container">
          <div className="about-cta__content">
            <h2>Ready to Experience FreshSteam?</h2>
            <p>
              Let us bring our passion for exceptional food and service to your next event.
              Contact us today to start planning your perfect catering experience.
            </p>
            <div className="about-cta__actions">
              <Link to="/menu">
                <Button variant="primary" size="lg">
                  View Our Menu
                </Button>
              </Link>
              <Link to="/contact">
                <Button variant="outline" size="lg">
                  Get in Touch
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
