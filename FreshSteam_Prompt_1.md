# Catering Business Website Development Prompt

## Project Overview
Create a professional home-based catering business website with online ordering capabilities, integrating Google Forms for order management while maintaining an Airbnb-inspired design aesthetic.

## Technical Requirements

### 1. Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla or React)
- **Styling**: Modern CSS with Flexbox/Grid, responsive design
- **Forms**: Google Forms integration via iframe/embed
- **Payment**: Stripe/PayPal integration or link to external payment processor
- **Hosting**: Netlify, Vercel, or GitHub Pages for static deployment

### 2. Design Specifications (Airbnb-Style)
- **Color Palette**: 
  - Primary: #FF5A5F (Airbnb red) or custom brand color
  - Secondary: #00A699 (teal accent)
  - Neutral: #767676, #484848, #222222
  - Background: #FFFFFF, #F7F7F7
- **Typography**: 
  - Headers: Circular, Helvetica, or similar clean sans-serif
  - Body: Open Sans or system fonts
  - Font weights: 300, 400, 600, 700
- **Layout**: 
  - Clean white space
  - Card-based components
  - Rounded corners (8px border-radius)
  - Subtle shadows: box-shadow: 0 2px 16px rgba(0,0,0,0.12)
- **Visual Elements**:
  - High-quality food photography
  - Gradient overlays on hero sections
  - Smooth hover animations
  - Professional icons (Feather or Heroicons)

### 3. Page Structure and Features

#### Homepage
- **Hero Section**: 
  - Large background image of signature dish
  - Compelling headline: "Exceptional Catering for Your Special Moments"
  - Subtitle with value proposition
  - Primary CTA button: "View Menu & Order"
- **Services Overview**: 
  - 3-4 service cards (Corporate Events, Weddings, Private Parties, etc.)
  - Brief descriptions with appealing imagery
- **Why Choose Us Section**:
  - 3 key differentiators with icons
  - Customer testimonials carousel
- **Featured Menu Items**:
  - Grid layout of 6-8 popular dishes
  - Hover effects revealing prices/descriptions
- **Call-to-Action Section**: 
  - Order now button
  - Contact information
  - Service area map/coverage

#### Menu & Ordering Page
- **Menu Categories**: 
  - Filterable sections (Appetizers, Mains, Desserts, Beverages)
  - Card-based layout with images, descriptions, prices
  - Dietary indicators (vegetarian, gluten-free, etc.)
- **Google Form Integration**:
  - Embedded order form in a styled container
  - Custom CSS to match site design
  - Form sections: Contact Info, Event Details, Menu Selection, Special Requests
- **Order Summary Preview**:
  - JavaScript-powered calculator (if possible without backend)
  - Estimated total display

#### About Page
- **Story Section**: Personal story, experience, passion for cooking
- **Team/Chef Introduction**: Professional photos and backgrounds
- **Service Process**: Step-by-step ordering and delivery process
- **Certifications**: Food safety, licenses, insurance information

#### Contact Page
- **Contact Form**: Simple inquiry form
- **Business Information**: Address, phone, email, hours
- **Service Coverage Map**: Visual representation of delivery areas
- **FAQ Section**: Common questions about catering services

### 4. Google Forms Integration Setup

#### Order Form Structure
Create a Google Form with these sections:
1. **Customer Information**
   - Name, Email, Phone
   - Event Date, Time, Location
   - Number of Guests
2. **Menu Selection**
   - Checkbox grids for each category
   - Quantity fields for each item
   - Special dietary requirements
3. **Event Details**
   - Event type (dropdown)
   - Setup requirements
   - Special instructions
4. **Budget and Payment**
   - Estimated budget range
   - Preferred payment method
   - Deposit payment confirmation

#### Styling the Embedded Form
```css
/* Custom CSS for Google Form */
.google-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.google-form-container iframe {
  width: 100%;
  min-height: 800px;
  border: none;
  border-radius: 8px;
}
```

### 5. Payment Integration Options

#### Option 1: PayPal/Stripe Links
- Add payment buttons after form submission
- Link to hosted payment pages
- Manual order confirmation process

#### Option 2: Payment Processor Integration
- Use Stripe Elements or PayPal SDK
- Collect payment information securely
- Integrate with Google Sheets for order tracking

### 6. Technical Implementation

#### Responsive Design
- Mobile-first approach
- Breakpoints: 320px, 768px, 1024px, 1440px
- Flexible grid system
- Touch-friendly buttons (minimum 44px)

#### Performance Optimization
- Optimize images (WebP format, lazy loading)
- Minimize CSS/JS bundles
- CDN integration for assets
- Google PageSpeed optimization

#### SEO and Analytics
- Meta tags and structured data
- Google Analytics integration
- Local SEO optimization
- Social media meta tags

### 7. Development Instructions

#### Project Structure
```
catering-website/
├── index.html
├── menu.html
├── about.html
├── contact.html
├── css/
│   ├── main.css
│   ├── components.css
│   └── responsive.css
├── js/
│   ├── main.js
│   ├── form-handler.js
│   └── menu-filter.js
├── images/
│   ├── hero/
│   ├── menu/
│   └── about/
└── assets/
    ├── icons/
    └── fonts/
```

#### Key Components to Build
1. **Navigation Bar**: Sticky header with smooth scrolling
2. **Hero Section**: Full-screen with background video/image
3. **Menu Cards**: Interactive cards with hover effects
4. **Form Container**: Styled wrapper for Google Forms
5. **Testimonials**: Slider/carousel component
6. **Footer**: Contact info, social links, legal pages

#### JavaScript Functionality
- Menu filtering and search
- Form validation and enhancement
- Smooth scrolling navigation
- Image lazy loading
- Contact form handling
- Order calculation (client-side)

### 8. Content Requirements

#### Copy and Messaging
- Professional, warm, and inviting tone
- Focus on quality, reliability, and personal service
- Clear value propositions
- Strong calls-to-action
- Customer testimonials and reviews

#### Visual Assets Needed
- High-quality food photography (minimum 1920x1080)
- Professional headshots
- Event photos/portfolio
- Logo and branding elements
- Icons and graphics

### 9. Launch and Maintenance

#### Pre-Launch Checklist
- Cross-browser testing
- Mobile responsiveness verification
- Form submission testing
- Payment integration testing
- SEO optimization
- Performance audit

#### Post-Launch
- Google Analytics setup
- Search Console configuration
- Social media integration
- Regular content updates
- Customer feedback collection

### 10. Additional Features (Phase 2)
- Online booking calendar
- Customer review system
- Email newsletter signup
- Social media feed integration
- Multi-language support (if needed)
- Advanced order tracking

## Example Google Form Questions

1. **Event Information**
   - Event Date (Date picker)
   - Event Time (Time picker)
   - Event Location (Short answer)
   - Number of Guests (Number)
   - Event Type (Dropdown: Wedding, Corporate, Birthday, etc.)

2. **Menu Selection**
   - Appetizers (Checkbox grid with quantities)
   - Main Courses (Checkbox grid with quantities)
   - Desserts (Checkbox grid with quantities)
   - Beverages (Checkbox grid with quantities)
   - Special Dietary Requirements (Paragraph text)

3. **Service Details**
   - Service Type (Multiple choice: Drop-off, Setup, Full Service)
   - Equipment Needed (Checkboxes: Tables, Chairs, Linens, etc.)
   - Special Instructions (Paragraph text)

4. **Contact & Payment**
   - Estimated Budget (Multiple choice ranges)
   - Payment Method Preference (Multiple choice)
   - How did you hear about us? (Dropdown)

## Budget Estimation
- **Development Time**: 40-60 hours
- **Design**: 15-20 hours
- **Development**: 20-30 hours
- **Testing & Deployment**: 5-10 hours
- **Ongoing Maintenance**: 2-4 hours/month

## Success Metrics
- Form completion rate > 80%
- Mobile traffic > 60%
- Average time on site > 3 minutes
- Conversion rate (visitors to orders) > 2%

This comprehensive approach will create a professional, Airbnb-style catering website that effectively integrates with Google Forms while providing an excellent user experience for your customers.